import { Image, imageService } from '@/services/api/imageService';
import { formatErrorMessage } from '@/lib/react-query/error-handler';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { API_BASE_URL } from '@/services/api';

export function AdminImageList() {
  const queryClient = useQueryClient();
  const { data: dataImages, isLoading, isError, error: queryError } = useQuery<Image[], Error>({
    queryKey: ['images'],
    queryFn: () => imageService.getAllImages()
  });

  // Import and use the deleteImage mutation hook
  const deleteImageMutation = useMutation({
    mutationFn: (id: number) => imageService.deleteImage(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['images'] });
    }
  });

  const errorMessage = queryError ? formatErrorMessage(queryError) : null;

  const handleDeleteImage = async (id: number) => {
    if (!confirm('Are you sure you want to delete this image?')) {
      return;
    }

    try {
      // Utiliser la mutation pour supprimer l'image
      await deleteImageMutation.mutateAsync(id);
    } catch (error) {
      console.error('Error deleting image:', error);
      alert('Failed to delete image. Please try again later.');
    }
  };

  const getImageSrc = (image: Image) : string => {
    return `${API_BASE_URL}/images/file/${image.fileName}`;
  };

  if (isLoading) {
    return <div className="p-4 text-center">Loading images...</div>;
  }

  if (isError && queryError) {
    return <div className="p-4 text-center text-red-500">{errorMessage}</div>;
  }

  if (dataImages?.length === 0) {
    return <div className="p-4 text-center">No images found. Upload your first image!</div>;
  }

  return (
    <div className="mt-8">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Existing Images</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {dataImages?.map((image: Image) => (
          <div key={image.id} className="border rounded-lg overflow-hidden bg-white">
            <div className="aspect-w-16 aspect-h-9 bg-gray-200">
              <img
                src={ getImageSrc(image) || `api/images/${image.id}`}
                alt={image.name}
                className="object-cover w-full h-full"
                onError={(e) => {
                  e.currentTarget.src = './images/image-not-found.svg';
                }}
              />
            </div>
            <div className="p-4">
              <h4 className="font-semibold text-gray-900">{image.name}</h4>
              <p className="text-sm text-gray-500 mt-1">Type: {image.fileType}</p>
              <p className="text-sm text-gray-500">File: {image.fileName}</p>
              <p className="text-xs text-gray-400 mt-2">
                Created: {new Date(image.createdAt).toLocaleDateString()}
              </p>
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => handleDeleteImage(image.id)}
                  disabled={deleteImageMutation.isPending}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors disabled:bg-red-300 disabled:cursor-not-allowed"
                >
                  {deleteImageMutation.isPending && deleteImageMutation.variables === image.id ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
