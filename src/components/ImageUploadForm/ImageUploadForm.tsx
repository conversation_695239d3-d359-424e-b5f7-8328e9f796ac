import {
  useForm,
} from '@tanstack/react-form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { formatErrorMessage } from '@/lib/react-query/error-handler';
import { CreateImageDto, imageService } from '@/services/api/imageService';

interface ImageFormValues {
  file: File | null;
}

export function ImageUploadForm() {
  const queryClient = useQueryClient();

  // Formulaire TanStack Form
  const form = useForm({
    defaultValues: { file: null } as ImageFormValues,
    onSubmit: async ({ value }) => {
      const file = value.file;
      if (!file) return;
      const baseName = file.name.split('.').slice(0, -1).join('.') || file.name;
      await createImageMutation.mutateAsync({ name: baseName, file });
    },
  }) as UseFormReturn<ImageFormValues>;

  // Mutation pour créer une image
  const createImageMutation = useMutation({
    mutationFn: (data: CreateImageDto) => imageService.createImage(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['images'] });
      form.reset();
    },
  });

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Upload New Image</h2>
      <form onSubmit={form.handleSubmit} noValidate>
        {/* Champ Fichier */}
        <form.Field
          name="file"
          validate={(file: File) => (file ? undefined : 'Veuillez sélectionner un fichier.')}
        >
          {({ field } : { field: any }) => (
            <div className="mb-6">
              <label htmlFor="file" className="block text-sm font-medium text-gray-700 mb-1">
                Image File
              </label>
              <input
                id="file"
                type="file"
                accept="image/*"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.files?.[0] ?? null)}
              />
              {field.state.error && (
                <div className="text-red-500 text-sm mt-1">{field.state.error}</div>
              )}
            </div>
          )}
        </form.Field>

        {createImageMutation.isError && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {formatErrorMessage(createImageMutation.error as Error)}
          </div>
        )}

        {createImageMutation.isSuccess && (
          <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
            Image uploaded successfully!
          </div>
        )}

        <Button type="submit" disabled={createImageMutation.isPending} className="w-full">
          {createImageMutation.isPending ? 'Uploading...' : 'Upload Image'}
        </Button>
      </form>
    </div>
  )
}
